# Deployment Guide

## Project Structure
- `my-next-app/` - Next.js frontend application
- `backend-v2/` - Node.js/Express backend API
- `backend-chatbot/` - Python FastAPI chatbot service

## Environment Variables

### Frontend (my-next-app)
Create `.env.local` file:
```bash
NEXT_PUBLIC_API_URL=https://your-backend-url.vercel.app
NEXT_PUBLIC_CHATBOT_API_URL=https://your-chatbot-url.vercel.app
NEXT_PUBLIC_HOTEL_API_KEY=your_rapidapi_key_here
NEXT_PUBLIC_HOTEL_API_HOST=booking-com15.p.rapidapi.com
```

### Backend (backend-v2)
Create `.env` file:
```bash
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret_here
PORT=5000
```

## Deployment Steps

### 1. Deploy Backend to Vercel
1. Push `backend-v2` to a separate GitHub repository
2. Connect to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy

### 2. Deploy Frontend to Vercel
1. Push `my-next-app` to GitHub repository
2. Connect to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy

### 3. Deploy Chatbot (Optional)
Deploy `backend-chatbot` to a Python hosting service like Railway or Render.

## Important Notes
- All hardcoded localhost URLs have been replaced with environment variables
- API keys are no longer hardcoded
- CORS is configured for production
- Database connection is environment-based

## Pre-deployment Checklist
- [ ] Environment variables configured
- [ ] Build process tested locally
- [ ] All hardcoded URLs removed
- [ ] API keys secured
- [ ] Database accessible from production

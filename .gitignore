# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
../backend-v2
/node_modules
/.pnp
.pnp.*
.pnp.js
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local
**/.env
**/.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Batch files for local development
*.bat

# Duplicate root level files (keep only my-next-app version)
/app/
/components/
/context/
/data/
/lib/
/pages/
/public/
/services/
/types/
/utils/
/eslint.config.mjs
/middleware.ts
/next.config.js
/next.config.ts
/package.json
/package-lock.json
/postcss.config.mjs
/tailwind.config.js
/tsconfig.json
/types.ts

# Backend folder (old version)
/backend/
